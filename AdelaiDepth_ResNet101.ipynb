{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"name": "AdelaiDepth ResNet101", "provenance": [], "collapsed_sections": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "accelerator": "GPU"}, "cells": [{"cell_type": "markdown", "metadata": {"id": "al6R1WJ8SAP-"}, "source": ["This is the AdelaiDepth with ResNet101 pretrained model demo colab notebook, here you can try AdelaiDepth. To perfect work run cells sequentially. If you want to upload some new images, run from 2nd cell.\n", "Here the main branch -\n", "https://github.com/aim-uofa/Adelai<PERSON>epth"]}, {"cell_type": "code", "metadata": {"id": "IgC0hSmH-iq-", "cellView": "form"}, "source": ["#@title Download libraries and clone git project(run only for first time)\n", "%cd /content/\n", "!git clone https://github.com/aim-uofa/AdelaiDepth\n", "!wget -O /content/AdelaiDepth/res101.pth https://cloudstor.aarnet.edu.au/plus/s/lTIJF4vrvHCAI31/download\n", "import sys\n", "import os\n", "#sys.path.append('/usr/local/lib/python3.7/site-packages')\n", "sys.path.append('/content/AdelaiDepth/LeReS')\n", "#os.environ[\"PYTHONPATH\"] += (\":/usr/local/lib/python3.7/site-package\")\n", "os.environ[\"PYTHONPATH\"] += (\":/content/AdelaiDepth/LeReS\")\n", "!echo \"$PYTHONPATH\"\n", "!pip3 install ipykernel matplotlib opencv-python\n", "!pip3 install torch==1.6.0 torchvision==0.7.0\n", "#cudatoolkit==10.2\n", "from IPython.display import clear_output\n", "clear_output()\n"], "execution_count": 1, "outputs": []}, {"cell_type": "code", "metadata": {"id": "XXf26xd_Scfj", "cellView": "form"}, "source": ["#@title Clean output directory\n", "%%shell\n", "if [ -e /content/AdelaiDepth/LeReS/test_images ]; then\n", "  if [ -n \"`ls -A /content/AdelaiDepth/LeReS/test_images/`\" ]; then\n", "    rm -rf /content/AdelaiDepth/LeReS/test_images/*\n", "    echo \"Cleaned\"\n", "  fi\n", "elif [ ! -e /content/AdelaiDepth/LeReS/test_images ]; then\n", "  mkdir /content/AdelaiDepth/LeReS/test_images\n", "  mkdir /content/AdelaiDepth/LeReS/test_images/outputs\n", "  echo \"Created directories and cleaned\"\n", "fi"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "EqELF486_s98", "cellView": "form"}, "source": ["#@title Upload images(png, jpg works fine, also you can upload multiple images)\n", "%cd /content/AdelaiDepth/LeReS/test_images/\n", "from google.colab import files\n", "image = files.upload()\n", "#for n in image.keys():\n", "#  print(\"{name} succesfully uploaded!\".format(name = n))"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "I57K7VLfQwfC", "cellView": "form"}, "source": ["#@title Run network\n", "!echo \"$PYTHONPATH\"\n", "%cd /content/AdelaiDepth\n", "!python3 LeReS/tools/test_depth.py --load_ckpt res101.pth --backbone resnext101\n", "from IPython.display import clear_output\n", "clear_output()\n", "!echo \"Done!\""], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "vy-oT9YKgMl3", "cellView": "form"}, "source": ["#@title Download results\n", "from google.colab import files\n", "%cd /content/AdelaiDepth/LeReS/test_images/\n", "!find outputs/ -name \"*-depth_raw.png\" | zip -r result.zip -@\n", "files.download(\"result.zip\")"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "9ZDuKV3KvTm8", "cellView": "form"}, "source": ["#@title Check videocard\n", "!nvidia-smi"], "execution_count": null, "outputs": []}]}